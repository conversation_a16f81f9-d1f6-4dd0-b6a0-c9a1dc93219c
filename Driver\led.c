#include <led.h>
#include <math.h>

uint8_t temp=0x00;
uint8_t temp_old=0xff;
uint8_t ucled[3]={1,1,1};
uint8_t beep_enable=0;
uint8_t led2_lightflag;
uint8_t time_200ms;
uint8_t led2flag;


void led_gpio_init(void)
{
 RCC_APB2PeriphClockCmd (RCC_APB2Periph_GPIOA ,ENABLE);
 GPIO_InitTypeDef   GPIO_Initstructure;
 GPIO_Initstructure.GPIO_Mode=GPIO_Mode_Out_PP;
// GPIO_Initstructure.GPIO_Pin=GPIO_Pin_0|GPIO_Pin_1|GPIO_Pin_2;
 GPIO_Initstructure.GPIO_Pin=GPIO_Pin_1|GPIO_Pin_2;
 GPIO_Initstructure.GPIO_Speed= GPIO_Speed_50MHz;
 GPIO_Init (GPIOA ,&GPIO_Initstructure);
}

void led_disp(uint8_t *ucled)
{
 uint8_t i=0;
	temp =0x00;
	for(i=0;i<3;i++)
	temp|=(ucled [i]<<i);
	
	if(temp !=temp_old )
	{
//	 (temp&0x01)?GPIO_SetBits(GPIOA ,GPIO_Pin_0):GPIO_ResetBits (GPIOA ,GPIO_Pin_0);
	  (temp&0x02)?GPIO_SetBits(GPIOA ,GPIO_Pin_1):GPIO_ResetBits (GPIOA ,GPIO_Pin_1);
		(temp&0x04)?GPIO_SetBits(GPIOA ,GPIO_Pin_2):GPIO_ResetBits (GPIOA ,GPIO_Pin_2);
//		(temp&0x08)?GPIO_SetBits(GPIOA ,GPIO_Pin_3):GPIO_ResetBits (GPIOA ,GPIO_Pin_3);
//		(temp&0x10)?GPIO_SetBits(GPIOA ,GPIO_Pin_4):GPIO_ResetBits (GPIOA ,GPIO_Pin_4);
//		(temp&0x20)?GPIO_SetBits(GPIOA ,GPIO_Pin_5):GPIO_ResetBits (GPIOA ,GPIO_Pin_5);
	
	temp_old =temp;
	}
}


void led_task(void)
{
//	static const uint32_t breathperiod=2000;
//  static 	uint32_t breathcounter;
//  static const 	uint8_t pwmmax=10;
//	static uint8_t breathness;
//  static  uint8_t pwmcounter;
//	
//	breathcounter =(breathcounter +1)%breathperiod ;
//	
//	breathness=(uint8_t)(pwmmax*(sinf((2.0f*3.14159f/breathperiod)*breathcounter)+1.0f)/2.0f);
//	
//	pwmcounter =(pwmcounter +1)%pwmmax ;
//	
//	ucled[2]=(pwmcounter<breathness)?1:0;
	
	
	
	


	if(led2flag)
	ucled[1]=led2_lightflag;
	else ucled[1]=0;
	

	
	
	
  led_disp (ucled);
	beep_task(beep_enable);

}


void beep_gpio_init(void)
{
RCC_APB2PeriphClockCmd (RCC_APB2Periph_GPIOB ,ENABLE);
GPIO_InitTypeDef GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode = GPIO_Mode_Out_PP;
GPIO_Initstructure.GPIO_Pin=GPIO_Pin_15 ;
GPIO_Initstructure.GPIO_Speed =GPIO_Speed_50MHz;
GPIO_Init(GPIOB ,&GPIO_Initstructure);
}


void beep_task(uint8_t enable)
{
if(enable)
{
	GPIO_ResetBits(GPIOB ,GPIO_Pin_15);
}
else 
	GPIO_SetBits(GPIOB ,GPIO_Pin_15);
}


