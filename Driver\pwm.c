#include <pwm.h>


//占空比=CCR/（ARR+1）——周期和定时器一样——分辨率=1/（ARR+1）//



void pwm_init(void)
{
RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2,ENABLE);
RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);

//GPIO_PinRemapConfig() 重映射函数

GPIO_InitTypeDef 	GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode=GPIO_Mode_AF_PP;	
GPIO_Initstructure.GPIO_Pin=GPIO_Pin_0;
GPIO_Initstructure.GPIO_Speed=GPIO_Speed_50MHz;	
GPIO_Init(GPIOA,&GPIO_Initstructure);	
	
	
TIM_InternalClockConfig(TIM2);

TIM_TimeBaseInitTypeDef TIM_TimeBasenitstructure;
TIM_TimeBasenitstructure.TIM_ClockDivision=TIM_CKD_DIV1;
TIM_TimeBasenitstructure.TIM_CounterMode=TIM_CounterMode_Up;
TIM_TimeBasenitstructure.TIM_Period=100-1;
TIM_TimeBasenitstructure.TIM_Prescaler=720-1;
TIM_TimeBasenitstructure.TIM_RepetitionCounter=0;
TIM_TimeBaseInit(TIM2,&TIM_TimeBasenitstructure);

TIM_OCInitTypeDef TIM_OutputCompareInitstructure;
TIM_OCStructInit(&TIM_OutputCompareInitstructure);
TIM_OutputCompareInitstructure.TIM_OCMode=TIM_OCMode_PWM1;	
TIM_OutputCompareInitstructure.TIM_OCPolarity=TIM_OCPolarity_High;	
TIM_OutputCompareInitstructure.TIM_OutputState=TIM_OutputState_Enable;
TIM_OutputCompareInitstructure.TIM_Pulse=50;	 //CCR 
TIM_OC1Init(TIM2,&TIM_OutputCompareInitstructure);	//不同的通道有不同的函数

TIM_Cmd(TIM2,ENABLE);	
}



void pwm_setcompare_1(uint16_t compare)
{
TIM_SetCompare1(TIM2,compare);
}



