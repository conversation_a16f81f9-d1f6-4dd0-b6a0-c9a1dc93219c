{"name": "demo_01", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Start", "files": [{"path": "Start/startup_stm32f10x_md.s"}, {"path": "Start/core_cm3.c"}, {"path": "Start/core_cm3.h"}, {"path": "Start/stm32f10x.h"}, {"path": "Start/system_stm32f10x.c"}, {"path": "Start/system_stm32f10x.h"}], "folders": []}, {"name": "Library", "files": [{"path": "Library/misc.c"}, {"path": "Library/misc.h"}, {"path": "Library/stm32f10x_adc.c"}, {"path": "Library/stm32f10x_adc.h"}, {"path": "Library/stm32f10x_bkp.c"}, {"path": "Library/stm32f10x_bkp.h"}, {"path": "Library/stm32f10x_can.c"}, {"path": "Library/stm32f10x_can.h"}, {"path": "Library/stm32f10x_cec.c"}, {"path": "Library/stm32f10x_cec.h"}, {"path": "Library/stm32f10x_crc.c"}, {"path": "Library/stm32f10x_crc.h"}, {"path": "Library/stm32f10x_dac.c"}, {"path": "Library/stm32f10x_dac.h"}, {"path": "Library/stm32f10x_dbgmcu.c"}, {"path": "Library/stm32f10x_dbgmcu.h"}, {"path": "Library/stm32f10x_dma.c"}, {"path": "Library/stm32f10x_dma.h"}, {"path": "Library/stm32f10x_exti.c"}, {"path": "Library/stm32f10x_exti.h"}, {"path": "Library/stm32f10x_flash.c"}, {"path": "Library/stm32f10x_flash.h"}, {"path": "Library/stm32f10x_fsmc.c"}, {"path": "Library/stm32f10x_fsmc.h"}, {"path": "Library/stm32f10x_gpio.c"}, {"path": "Library/stm32f10x_gpio.h"}, {"path": "Library/stm32f10x_i2c.c"}, {"path": "Library/stm32f10x_i2c.h"}, {"path": "Library/stm32f10x_iwdg.c"}, {"path": "Library/stm32f10x_iwdg.h"}, {"path": "Library/stm32f10x_pwr.c"}, {"path": "Library/stm32f10x_pwr.h"}, {"path": "Library/stm32f10x_rcc.c"}, {"path": "Library/stm32f10x_rcc.h"}, {"path": "Library/stm32f10x_rtc.c"}, {"path": "Library/stm32f10x_rtc.h"}, {"path": "Library/stm32f10x_sdio.c"}, {"path": "Library/stm32f10x_sdio.h"}, {"path": "Library/stm32f10x_spi.c"}, {"path": "Library/stm32f10x_spi.h"}, {"path": "Library/stm32f10x_tim.c"}, {"path": "Library/stm32f10x_tim.h"}, {"path": "Library/stm32f10x_usart.c"}, {"path": "Library/stm32f10x_usart.h"}, {"path": "Library/stm32f10x_wwdg.c"}, {"path": "Library/stm32f10x_wwdg.h"}], "folders": []}, {"name": "System", "files": [{"path": "System/Timer.c"}], "folders": []}, {"name": "component", "files": [{"path": "component/OLED.c"}, {"path": "component/OLED.h"}, {"path": "component/OLED_Font.h"}], "folders": []}, {"name": "User", "files": [{"path": "main.c"}, {"path": "User/stm32f10x_conf.h"}, {"path": "User/stm32f10x_it.c"}, {"path": "User/stm32f10x_it.h"}], "folders": []}, {"name": "Driver", "files": [{"path": "Driver/scheduler.c"}, {"path": "Driver/mydefine.h"}, {"path": "Driver/led.c"}, {"path": "Driver/systick.c"}, {"path": "Driver/key.c"}, {"path": "Driver/oled_app.c"}, {"path": "Driver/sensorcounter.c"}, {"path": "Driver/pwm.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "beb2072bbe88c747a49bded39149221a"}, "targets": {"demo_01": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "STLink", "uploadConfig": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/demo_01.st.option.bytes.ini", "otherCmds": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["Start", "User", "Library", "Driver", "component", "System", ".cmsis/include", "RTE/_demo_01"], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER", "STM32F10X_MD"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings", "gnu-extensions": true}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000", "$disableOutputTask": true}}}}}, "version": "3.6"}