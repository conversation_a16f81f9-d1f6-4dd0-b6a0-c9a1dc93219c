#include <Timer.h>

                                      //定时器配置//
/*rcc开启时钟--选择时基单元的时钟源对于定时中断选择内部时钟源--配置时基单元（psc预分频，arr自动重装，cnt计数器）
--配置输出中断控制允许更新中断输出到NVIC--配置NVIC--运行控制--使能计数器*/



void Timer_init(void)
{
RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2,ENABLE);
	
TIM_InternalClockConfig(TIM2);

TIM_TimeBaseInitTypeDef TIM_TimeBaseInitstructure;
TIM_TimeBaseInitstructure.TIM_ClockDivision=TIM_CKD_DIV1;
TIM_TimeBaseInitstructure.TIM_CounterMode=TIM_CounterMode_Up;   //计数模式
TIM_TimeBaseInitstructure.TIM_Period=100-1;            //ARR  
TIM_TimeBaseInitstructure.TIM_Prescaler=720-1;          //PSC
TIM_TimeBaseInitstructure.TIM_RepetitionCounter=0;  //高级定时器才有//
TIM_TimeBaseInit(TIM2,&TIM_TimeBaseInitstructure);
	
	TIM_ClearFlag(TIM2,TIM_FLAG_Update);//
	
TIM_ITConfig(TIM2,TIM_IT_Update,ENABLE);
	
NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
NVIC_InitTypeDef NVIC_InitStructure;
NVIC_InitStructure.NVIC_IRQChannel=TIM2_IRQn;                   
NVIC_InitStructure.NVIC_IRQChannelCmd=ENABLE;
NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=2;
NVIC_InitStructure.NVIC_IRQChannelSubPriority=1;
NVIC_Init(&NVIC_InitStructure);


TIM_Cmd(TIM2,ENABLE);	
}













